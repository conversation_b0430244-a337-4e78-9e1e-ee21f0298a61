{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*资质管理用于管理平台商品分类所需的各种资质证件。</p>
                        <p>*资质可以关联到商品分类，用于商品资质管控，请谨慎操作。</p>
                    </div>
                </div>
            </div>
        </div>

        <!--搜索条件-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">资质名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" id="qualification_name" placeholder="请输入资质名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">绑定分类：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="category_name" id="category_name" placeholder="请输入分类名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
              
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="qualification-search">
                        <i class="layui-icon layui-icon-search"></i>查询
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="qualification-clear-search">重置</button>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <!--添加按钮-->
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-qualification {$view_theme_color}" data-type="add">新增资质</button>
            </div>

            <!--表格-->
            <table id="qualification-lists" lay-filter="qualification-lists"></table>

            <script type="text/html" id="statusTpl">
                <input type="checkbox" lay-filter="switch-status" data-id={{d.id}} data-field='status' lay-skin="switch"
                       lay-text="启用|禁用" {{# if(d.status){ }} checked {{# } }} />
            </script>

            <script type="text/html" id="requiredTpl">
                <input type="checkbox" lay-filter="switch-required" data-id={{d.id}} data-field='is_required' lay-skin="switch"
                       lay-text="必传|非必传" {{# if(d.is_required){ }} checked {{# } }} />
            </script>
             <script type="text/html" id="ex_imgTpl">
                <img src="{{d.ex_img}}" style="height:80px;width: 80px;margin-right: 10px;" class="image-show">
            </script>
            <script type="text/html" id="categoriesTpl">
                {{# if(d.bound_categories && d.bound_categories.length > 0){ }}
                    {{# layui.each(d.bound_categories, function(index, item){ }}
                        <span class="layui-badge layui-bg-blue" style="margin: 2px;">{{item}}</span>
                    {{# }); }}
                {{# } else { }}
                    <span style="color: #999;">未绑定</span>
                {{# } }}
            </script>

            <script type="text/html" id="documentTpl">
                {{# if(d.document_path && d.document_path !== ''){ }}
                    <a href="{{d.document_path}}" target="_blank" style="color: #5FB878;">
                        <i class="layui-icon layui-icon-file"></i> {{d.document_name || '查看文档'}}
                    </a>
                {{# } else { }}
                    <span style="color: #999;">未上传</span>
                {{# } }}
            </script>

            <script type="text/html" id="qualification-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="bind-categories"><i class="layui-icon layui-icon-tree"></i>绑定分类</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
            </script>
        </div>
    </div>
</div>

<script>

    layui.use(['table', 'form'], function(){
        var form = layui.form
            ,table = layui.table;

        //事件
        var active = {
            add: function(){
                layer.open({
                    type: 2
                    ,title: '新增资质'
                    ,content: '{:url("goods.qualification/add")}'
                    ,area: ['60%', '60%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'qualification-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("goods.qualification/add")}',
                               data:field,
                               type:"post",
                               success:function(res)
                               {
                                   if(res.code == 1) {
                                       layui.layer.msg(res.msg, {
                                           offset: '15px'
                                           , icon: 1
                                           , time: 1000
                                       });
                                       layer.close(index);
                                       table.reload('qualification-lists');
                                   }
                               }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            }
        };
        $('.layui-btn.layuiadmin-btn-qualification').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        //监听搜索
        form.on('submit(qualification-search)', function(data){
            var field = data.field;
            var searchParams = {};

            // 只传递有值的搜索参数
            if (field.name && String(field.name).trim() !== '') {
                searchParams.name = String(field.name).trim();
            }
            if (field.category_name && String(field.category_name).trim() !== '') {
                searchParams.category_name = String(field.category_name).trim();
            }
            if (field.status && String(field.status) !== '') {
                searchParams.status = String(field.status);
            }

            console.log('搜索参数:', searchParams); // 调试信息

            //执行重载
            table.reload('qualification-lists', {
                where: searchParams,
                page: {
                    curr: 1
                }
            });
            return false; // 阻止表单默认提交
        });

        //清空查询
        form.on('submit(qualification-clear-search)', function(){
            $('input[name="name"]').val('');
            $('input[name="category_name"]').val('');
            $('select[name="status"]').val('');
            form.render('select');
            //刷新列表
            table.reload('qualification-lists', {
                where: {},
                page: {
                    curr: 1
                }
            });
            return false; // 阻止表单默认提交
        });


        like.tableLists('#qualification-lists', '{:url("goods.qualification/lists")}', [
            {field: 'id', width: 60, title: 'ID', sort: true}
            ,{field: 'name', title: '资质名称', align:"center"}
            ,{field: 'description', title: '资质描述', align:"center"}
            ,{field: 'bound_categories', title: '绑定分类', width: 200, align:"center", templet:'#categoriesTpl'}
            ,{field: 'document_path', title: '文档上传', width: 150, align:"center", templet:'#documentTpl'}
            ,{field: 'is_required', title: '是否必传', width: 120, align:"center", templet:'#requiredTpl'}
             ,{field: 'ex_img', title: '示例', align:"center",width: 150,templet:'#ex_imgTpl'}
            ,{field: 'status', title: '状态', align:"center", templet:'#statusTpl'}
            ,{field: 'sort', title: '排序', width: 80, align:"center", sort: true}
            ,{title: '操作', align: 'center', fixed: 'right', toolbar: '#qualification-operation'}
        ]);


        form.on('switch(switch-status)',function (obj) {
            var id = obj.elem.attributes['data-id'].nodeValue;
            var status = 0;
            if(this.checked){
                status = 1;
            }
            like.ajax({
                url:'{:url("goods.qualification/switchStatus")}',
                data:{id:id,status:status},
                type:'post',
                success:function (res) {
                    if(res.code == 1) {
                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                    }
                }
            });
        });

        form.on('switch(switch-required)',function (obj) {
            var id = obj.elem.attributes['data-id'].nodeValue;
            var is_required = 0;
            if(this.checked){
                is_required = 1;
            }
            like.ajax({
                url:'{:url("goods.qualification/switchRequired")}',
                data:{id:id,is_required:is_required},
                type:'post',
                success:function (res) {
                    if(res.code == 1) {
                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                    }
                }
            });
        });
  $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,1000);
        });

        //监听工具条
        table.on('tool(qualification-lists)', function(obj){
            if(obj.event === 'del'){
                var id = obj.data.id;
                var unitName = "<span style='color: red;'>"+obj.data.name+"</span>";
                layer.confirm('确定删除资质: '+unitName, function(index){
                    like.ajax({
                        url:'{:url("goods.qualification/del")}',
                        data:{'id':id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                obj.del();
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index);
                            }
                        }
                    });
                });

            }else if(obj.event === 'edit'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑资质'
                    ,content: '{:url("goods.qualification/edit")}?id='+id
                    ,area: ['60%', '60%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'qualification-submit-edit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("goods.qualification/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('qualification-lists');
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                })
            }else if(obj.event === 'bind-categories'){
                var id = obj.data.id;
                var name = obj.data.name;
                layer.open({
                    type: 2
                    ,title: '绑定分类 - ' + name
                    ,content: '{:url("goods.qualification/bindCategories")}?id='+id
                    ,area: ['80%', '80%']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index];
                        var checkedData = iframeWindow.getCheckedCategories();

                        like.ajax({
                            url:'{:url("goods.qualification/saveBinding")}',
                            data:{
                                qualification_id: id,
                                category_ids: checkedData
                            },
                            type:"post",
                            success:function(res)
                            {
                                if(res.code == 1) {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index);
                                }
                            }
                        });
                    }
                })
            }
        });
    });
</script>
